package api

import (
	"net/http"
	"qs/internal/dto"

	"github.com/gin-gonic/gin"
)

type HealthHandler struct{}

func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// HealthCheck 健康檢查
// @Summary 健康檢查
// @Description 檢查 API 服務是否正常運行
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} dto.APIResponse
// @Router /health [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, dto.SuccessResponse("服務運行正常", gin.H{
		"status": "healthy",
		"service": "QS API",
	}))
}

// RegisterRoutes 註冊路由
func (h *HealthHandler) RegisterRoutes(router *gin.Engine) {
	router.GET("/health", h.HealthCheck)
}
