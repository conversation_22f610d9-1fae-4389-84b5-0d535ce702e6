package api

import (
	"net/http"

	"qs/internal/dto"
	"qs/internal/service"

	"github.com/gin-gonic/gin"
)

type BranchHandler struct {
	branchService service.BranchService
}

func NewBranchHandler(branchService service.BranchService) *BranchHandler {
	return &BranchHandler{
		branchService: branchService,
	}
}

// GetBranches 取得所有分店
// @Summary 取得所有分店列表
// @Description 取得系統中所有分店的基本資訊
// @Tags branches
// @Accept json
// @Produce json
// @Success 200 {object} dto.APIResponse{data=[]dto.BranchResponse}
// @Failure 500 {object} dto.APIResponse
// @Router /api/v1/branches [get]
func (h *BranchHandler) GetBranches(c *gin.Context) {
	branches, err := h.branchService.GetAllBranches()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("取得分店列表失敗", err))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("取得分店列表成功", branches))
}

// RegisterRoutes 註冊路由
func (h *BranchHandler) RegisterRoutes(router *gin.RouterGroup) {
	branches := router.Group("/branches")
	{
		branches.GET("", h.GetBranches)
	}
}
