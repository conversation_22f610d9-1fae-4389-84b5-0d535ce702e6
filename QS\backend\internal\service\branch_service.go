package service

import (
	"qs/internal/dto"
	"qs/internal/repository"
)

type BranchService interface {
	GetAllBranches() ([]dto.BranchResponse, error)
}

type branchService struct {
	branchRepo repository.BranchRepository
}

func NewBranchService(branchRepo repository.BranchRepository) BranchService {
	return &branchService{
		branchRepo: branchRepo,
	}
}

func (s *branchService) GetAllBranches() ([]dto.BranchResponse, error) {
	branches, err := s.branchRepo.GetAll()
	if err != nil {
		return nil, err
	}

	var response []dto.BranchResponse
	for _, branch := range branches {
		response = append(response, dto.BranchResponse{
			ID:   branch.ID,
			Name: branch.Name,
		})
	}

	return response, nil
}
