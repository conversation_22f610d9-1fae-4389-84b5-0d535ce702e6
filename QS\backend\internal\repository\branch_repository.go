package repository

import (
	"qs/internal/model"

	"gorm.io/gorm"
)

type BranchRepository interface {
	GetAll() ([]model.Branch, error)
	GetByID(id uint) (*model.Branch, error)
}

type branchRepository struct {
	db *gorm.DB
}

func NewBranchRepository(db *gorm.DB) BranchRepository {
	return &branchRepository{db: db}
}

func (r *branchRepository) GetAll() ([]model.Branch, error) {
	var branches []model.Branch
	err := r.db.Find(&branches).Error
	return branches, err
}

func (r *branchRepository) GetByID(id uint) (*model.Branch, error) {
	var branch model.Branch
	err := r.db.First(&branch, id).Error
	if err != nil {
		return nil, err
	}
	return &branch, nil
}
