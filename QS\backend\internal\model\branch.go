package model

import (
	"time"

	"gorm.io/gorm"
)

// Branch 分店模型
type Branch struct {
	ID uint `gorm:"primarykey" json:"id"`

	Name string `json:"name"`

	// Timing Database 連線設定
	TimingIP         string `json:"-"` // Host
	TimingPort       string `json:"-"`
	TimingDBUser     string `json:"-"`
	TimingDBPassword string `json:"-"`
	TimingDBName     string `json:"-"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
