# 建置階段
FROM golang:1.22-alpine AS builder

# 設定工作目錄
WORKDIR /app

# 安裝必要的套件
RUN apk add --no-cache git

# 複製 go mod 檔案
COPY go.mod go.sum ./

# 下載依賴
RUN go mod download

# 複製原始碼
COPY . .

# 建置應用程式
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd

# 執行階段
FROM alpine:latest

# 安裝 ca-certificates（用於 HTTPS 請求）
RUN apk --no-cache add ca-certificates

# 設定工作目錄
WORKDIR /root/

# 從建置階段複製執行檔
COPY --from=builder /app/main .

# 複製配置檔案
COPY --from=builder /app/config.yaml .

# 暴露埠號
EXPOSE 8080

# 執行應用程式
CMD ["./main"]
