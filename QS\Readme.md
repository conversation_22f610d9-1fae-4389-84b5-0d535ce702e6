# 診所看診進度系統

> **簡介**
> 此專案為小型診所使用的看診進度查詢系統，病患可以即時查看各分店（診所）當前各位醫師的看診進度（看診號碼、等待人數），介面支援手機/平板/桌面三種裝置的 RWD 排版。

---

## 主要功能

* 列出所有分店（Branch）並可切換分店（下拉 select）
* 以卡片（Card）方式顯示每日班次（由後端依設定回傳：早/午/晚班）、醫師姓名、目前看診號碼、等待人數
* 支援響應式設計（RWD），在桌機、平板與手機上皆有良好使用體驗
* 後端採 RESTful API、Golang（Gin + Gorm），前端採 Vue3 + TypeScript + Quasar UI
* 設計遵守 DRY、Clean Code、可擴充與高彈性原則

---

## 技術棧

* 前端：Vue 3, TypeScript, Quasar Framework
* 後端：Golang, Gin, Gorm
* 資料庫：MySQL（或相容關聯式 DB）
* 測試：Vitest（前端）、Go test（後端）、E2E 可選 Playwright/Cypress
* 部署：Docker / docker-compose
* 文件：OpenAPI（Swagger）建議

---

## 專案目錄範例

```
repo-root/
├─ frontend/            # Vue 3 + Quasar
│  ├─ src/
│  │  ├─ components/
│  │  │  ├─ BranchSelect.vue
│  │  │  ├─ DoctorCard.vue
│  │  ├─ pages/
│  │  │  ├─ Home.vue
│  │  ├─ composables/
│  │  ├─ plugins/
│  ├─ quasar.conf.js
├─ backend/             # Golang Gin + Gorm
│  ├─ cmd/
│  ├─ internal/
│  │  ├─ api/           # handlers
│  │  ├─ service/       # business logic
│  │  ├─ repository/    # DB access（使用 Gorm）
│  │  ├─ model/
│  │  ├─ dto/
│  ├─ migrations/
│  ├─ Dockerfile
├─ docker-compose.yml
├─ README.md
```

---

## API 設計（範例）

> 注意：以下為建議的 RESTful 介面與回傳格式，實務上建議加上 OpenAPI 規格並依需求擴充。

### 1. 取得所有分店

```
GET /api/v1/branches
Response 200
[
  { "id": 1, "name": "北門診所", "address": "..." },
  { "id": 2, "name": "東門診所", "address": "..." }
]
```
---

## 前端設計建議

* 使用 Quasar 的 Grid 與 Card 元件實作響應式佈局
* 主畫面組件：

  * `BranchSelect.vue`：負責列出分店並觸發切換
  * `DoctorCard.vue`：一張卡片呈現「班別標籤、醫師名稱、目前看診號碼、等待人數」
  * `Home.vue`：組合上方 select 與底下卡片列表（支援空資料時的 friendly message）
* 資料抓取：使用 `fetch` 或 `axios` 於 composable（例如 `useSchedule`）中集中處理，保持 DRY
* UI 優化：考慮使用 websocket 或 SSE（Server-Sent Events）做即時更新（若不需即時可定時 poll）
* 無障礙：為卡片與重要欄位加入 aria-label 與語意標籤

---

## 後端設計建議

* 層次分明：`handler(api)` -> `service` -> `repository`（Gorm）
* DTO 與 model 分離，避免直接傳遞 ORM 結構到外部
* 使用依賴注入（或 constructor injection）來提高測試性
* 提供 Swagger/OpenAPI 文件與一組範例 curl
* 建議自動化 migration（Gorm AutoMigrate 或 migration tool）

---

## 開發 & 本地啟動

**前端**

```bash
cd frontend
npm install
# 開發
npm run dev
# 建置
npm run build
```

**後端**

```bash
cd backend
# 設定環境變數（例）
# DATABASE_URL='user:pw@tcp(localhost:3306)/dbname?parseTime=true'
# GIN_MODE=debug

go mod tidy
# 執行
go run ./cmd/main.go
# 或 build
go build -o bin/server ./cmd
./bin/server
```

**使用 docker-compose（示範）**

```yaml
version: '3.8'
services:
  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: clinic
    ports: ["3306:3306"]
  backend:
    build: ./backend
    ports: ["8080:8080"]
    depends_on: [db]
  frontend:
    build: ./frontend
    ports: ["8081:80"]
```

---

## 測試

* 後端：`go test ./...`
* 前端：使用 Vitest `npm run test`，E2E 使用 Playwright 或 Cypress

---

## 部署建議

* 以容器化為主（Docker），加上簡單的 CI（GitHub Actions/GitLab CI）執行 lint、unit tests、build 與 image push
* 若需高可用，可把前端打成靜態檔由 CDN / NGINX 提供，後端採多實例 + 負載平衡

---

## 編碼規範（快速提示）

* 遵守 DRY：共用邏輯抽成 composable/service
* Clean Code：函式短小、命名清楚、避免副作用
* API 版本化：`
  /api/v1/...`
* 撰寫單元測試，保持 CI 綠燈
