# Go 參數
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=qs-server
BINARY_UNIX=$(BINARY_NAME)_unix

# 主要目標
.PHONY: all build clean test deps run dev

all: test build

# 建置
build:
	$(GOBUILD) -o bin/$(BINARY_NAME) -v ./cmd

# 建置 Linux 版本
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o bin/$(BINARY_UNIX) -v ./cmd

# 清理
clean:
	$(GOCLEAN)
	rm -f bin/$(BINARY_NAME)
	rm -f bin/$(BINARY_UNIX)

# 測試
test:
	$(GOTEST) -v ./...

# 下載依賴
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 執行（開發模式）
run:
	$(GOCMD) run ./cmd/main.go

# 開發模式（使用 air 進行熱重載，需要先安裝 air）
dev:
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air not found. Installing..."; \
		$(GOGET) -u github.com/cosmtrek/air; \
		air; \
	fi

# 格式化程式碼
fmt:
	$(GOCMD) fmt ./...

# 檢查程式碼
vet:
	$(GOCMD) vet ./...

# 安裝工具
install-tools:
	$(GOGET) -u github.com/cosmtrek/air
	$(GOGET) -u github.com/swaggo/swag/cmd/swag

# 生成 Swagger 文件
swagger:
	swag init -g cmd/main.go

# Docker 相關
docker-build:
	docker build -t qs-api .

docker-run:
	docker run -p 8080:8080 qs-api

# 幫助
help:
	@echo "Available commands:"
	@echo "  build        - Build the application"
	@echo "  build-linux  - Build for Linux"
	@echo "  clean        - Clean build files"
	@echo "  test         - Run tests"
	@echo "  deps         - Download dependencies"
	@echo "  run          - Run the application"
	@echo "  dev          - Run in development mode with hot reload"
	@echo "  fmt          - Format code"
	@echo "  vet          - Vet code"
	@echo "  install-tools- Install development tools"
	@echo "  swagger      - Generate Swagger documentation"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
