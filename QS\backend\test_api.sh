#!/bin/bash

# API 測試腳本
BASE_URL="http://localhost:8080"

echo "=== QS API 測試 ==="
echo

# 測試健康檢查
echo "1. 測試健康檢查..."
curl -s "$BASE_URL/health" | jq '.' || echo "健康檢查失敗"
echo
echo

# 測試取得所有分店
echo "2. 測試取得所有分店..."
curl -s "$BASE_URL/api/v1/branches" | jq '.' || echo "取得分店列表失敗"
echo
echo

# 測試不存在的分店
echo "3. 測試不存在的分店..."
curl -s "$BASE_URL/api/v1/branches/999/schedules" | jq '.' || echo "測試不存在分店失敗"
echo
echo

# 測試無效的分店 ID
echo "4. 測試無效的分店 ID..."
curl -s "$BASE_URL/api/v1/branches/invalid/schedules" | jq '.' || echo "測試無效分店 ID 失敗"
echo
echo

echo "=== 測試完成 ==="
