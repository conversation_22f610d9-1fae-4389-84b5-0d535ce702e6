# QS Backend 使用說明

## 快速啟動指南

### 1. 環境準備

確保您的系統已安裝：
- Go 1.22 或更高版本
- MySQL 資料庫
- SQL Server（用於 timing 資料庫）

### 2. 設定資料庫

#### 主資料庫 (MySQL)
```sql
CREATE DATABASE qs_main CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 環境變數設定
複製 `.env.example` 為 `.env` 並修改設定：

```bash
cp .env.example .env
```

編輯 `.env` 檔案：
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=qs_main
```

### 3. 啟動服務

```bash
# 安裝依賴
go mod tidy

# 插入測試資料（可選）
go run ./scripts/seed_data.go

# 啟動服務
go run ./cmd/main.go
```

服務將在 `http://localhost:8080` 啟動。

### 4. 測試 API

#### 使用 curl 測試

```bash
# 健康檢查
curl http://localhost:8080/health

# 取得所有分店
curl http://localhost:8080/api/v1/branches

# 取得指定分店的班次資訊
curl http://localhost:8080/api/v1/branches/1/schedules
```

#### 使用測試腳本

```bash
chmod +x test_api.sh
./test_api.sh
```

## API 詳細說明

### 健康檢查

**端點：** `GET /health`

**回應範例：**
```json
{
  "success": true,
  "message": "服務運行正常",
  "data": {
    "status": "healthy",
    "service": "QS API"
  }
}
```

### 取得所有分店

**端點：** `GET /api/v1/branches`

**回應範例：**
```json
{
  "success": true,
  "message": "取得分店列表成功",
  "data": [
    {
      "id": 1,
      "name": "北門診所",
      "address": "台北市中正區北門路100號",
      "phone": "02-1234-5678"
    },
    {
      "id": 2,
      "name": "東門診所",
      "address": "台北市中正區東門路200號",
      "phone": "02-2345-6789"
    }
  ]
}
```

### 取得分店班次資訊

**端點：** `GET /api/v1/branches/{id}/schedules`

**參數：**
- `id` (path): 分店 ID

**回應範例：**
```json
{
  "success": true,
  "message": "取得班次資訊成功",
  "data": {
    "branch_id": 1,
    "branch_name": "北門診所",
    "schedules": [
      {
        "id": 1,
        "doctor_name": "王醫師",
        "shift": "早班",
        "current_number": 15,
        "waiting_count": 3
      },
      {
        "id": 2,
        "doctor_name": "李醫師",
        "shift": "午班",
        "current_number": 8,
        "waiting_count": 5
      }
    ]
  }
}
```

**錯誤回應範例：**
```json
{
  "success": false,
  "message": "取得班次資訊失敗",
  "error": "分店不存在: record not found"
}
```

## 開發模式

### 熱重載開發

安裝 air 工具：
```bash
go install github.com/cosmtrek/air@latest
```

使用熱重載：
```bash
make dev
# 或
air
```

### 程式碼格式化

```bash
make fmt
make vet
```

### 執行測試

```bash
make test
```

## 部署

### Docker 部署

```bash
# 建置映像
make docker-build

# 執行容器
make docker-run
```

### 手動部署

```bash
# 建置執行檔
make build

# 執行
./bin/qs-server
```

## 故障排除

### 常見問題

1. **資料庫連線失敗**
   - 檢查資料庫服務是否啟動
   - 確認連線參數是否正確
   - 檢查防火牆設定

2. **Timing 資料庫連線失敗**
   - 確認 SQL Server 服務是否啟動
   - 檢查分店的 timing 資料庫設定
   - 確認網路連線

3. **埠號衝突**
   - 修改 `APP_PORT` 環境變數
   - 或在 config.yaml 中修改 port 設定

### 日誌查看

應用程式會輸出結構化日誌，包含：
- 啟動資訊
- 資料庫連線狀態
- API 請求日誌
- 錯誤資訊

在開發模式下，日誌會包含更詳細的除錯資訊。

## 擴展功能

### 添加新的 API 端點

1. 在 `internal/model` 中定義資料模型
2. 在 `internal/dto` 中定義 DTO
3. 在 `internal/repository` 中實作資料存取
4. 在 `internal/service` 中實作業務邏輯
5. 在 `internal/api` 中實作 HTTP handler
6. 在 `cmd/main.go` 中註冊路由

### 添加中介軟體

在 `initGin()` 函數中添加新的中介軟體：

```go
router.Use(yourMiddleware())
```

### 自訂配置

在 `config.yaml` 或環境變數中添加新的配置項目，並在 `setDefaults()` 函數中設定預設值。
