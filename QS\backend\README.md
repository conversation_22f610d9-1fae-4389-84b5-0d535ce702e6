# QS Backend API

診所看診進度系統後端 API，使用 Go + Gin + Gorm 開發。

## 功能特色

- RESTful API 設計
- 優雅的伺服器啟動與關閉
- 資料庫連線池管理
- 支援多資料庫連線（主資料庫 + 分店 timing 資料庫）
- 自動資料庫遷移
- CORS 支援
- 結構化日誌
- 配置檔案與環境變數支援

## 快速開始

### 1. 安裝依賴

```bash
go mod tidy
```

### 2. 設定環境變數

複製環境變數範例檔案：

```bash
cp .env.example .env
```

編輯 `.env` 檔案，設定資料庫連線資訊。

### 3. 啟動服務

```bash
# 開發模式
make run

# 或直接使用 go run
go run ./cmd/main.go
```

### 4. 測試 API

```bash
# 健康檢查
curl http://localhost:8080/health

# 取得所有分店
curl http://localhost:8080/api/v1/branches

# 取得指定分店的班次資訊
curl http://localhost:8080/api/v1/branches/1/schedules
```

## API 端點

### 健康檢查
- `GET /health` - 服務健康檢查

### 分店管理
- `GET /api/v1/branches` - 取得所有分店列表
- `GET /api/v1/branches/{id}/schedules` - 取得指定分店的班次資訊

## 專案結構

```
backend/
├── cmd/                    # 應用程式入口點
│   └── main.go
├── internal/               # 內部套件
│   ├── api/               # HTTP handlers
│   ├── service/           # 業務邏輯層
│   ├── repository/        # 資料存取層
│   ├── model/             # 資料模型
│   ├── dto/               # 資料傳輸物件
│   └── db/                # 資料庫連線
├── config.yaml            # 配置檔案
├── .env.example           # 環境變數範例
├── Makefile              # 建置腳本
└── README.md
```

## 開發工具

```bash
# 格式化程式碼
make fmt

# 檢查程式碼
make vet

# 執行測試
make test

# 建置應用程式
make build

# 開發模式（熱重載）
make dev
```

## 配置說明

### 環境變數

| 變數名稱 | 說明 | 預設值 |
|---------|------|--------|
| APP_PORT | 服務埠號 | 8080 |
| GIN_MODE | Gin 模式 | debug |
| DB_HOST | 主資料庫主機 | localhost |
| DB_PORT | 主資料庫埠號 | 3306 |
| DB_USER | 主資料庫使用者 | root |
| DB_PASSWORD | 主資料庫密碼 | password |
| DB_NAME | 主資料庫名稱 | qs_main |

### 資料庫設計

#### 主資料庫 (MySQL)
- `branches` - 分店資訊表
- `doctors` - 醫師資訊表

#### Timing 資料庫 (SQL Server)
- 每個分店有獨立的 timing 資料庫
- 包含班次、看診進度等即時資料

## 部署

### Docker 部署

```bash
# 建置 Docker 映像
make docker-build

# 執行容器
make docker-run
```

### 手動部署

```bash
# 建置執行檔
make build

# 執行
./bin/qs-server
```

## 注意事項

1. **資料庫連線**：主資料庫使用 MySQL，timing 資料庫使用 SQL Server
2. **連線池管理**：已設定適當的連線池參數
3. **優雅關閉**：支援 SIGINT/SIGTERM 信號的優雅關閉
4. **CORS**：已設定 CORS 支援前端跨域請求
5. **日誌**：結構化日誌輸出，支援不同日誌等級
