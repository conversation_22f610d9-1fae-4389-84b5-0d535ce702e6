package dto

// APIResponse 統一的 API 回應格式
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// BranchResponse 分店回應 DTO
type BranchResponse struct {
	ID      uint   `json:"id"`
	Name    string `json:"name"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
}

// DoctorScheduleResponse 醫師班次回應 DTO
type DoctorScheduleResponse struct {
	ID            uint   `json:"id"`
	DoctorName    string `json:"doctor_name"`
	Shift         string `json:"shift"`
	CurrentNumber int    `json:"current_number"`
	WaitingCount  int    `json:"waiting_count"`
}

// BranchScheduleResponse 分店班次回應 DTO
type BranchScheduleResponse struct {
	BranchID   uint                     `json:"branch_id"`
	BranchName string                   `json:"branch_name"`
	Schedules  []DoctorScheduleResponse `json:"schedules"`
}

// SuccessResponse 成功回應
func SuccessResponse(message string, data interface{}) APIResponse {
	return APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// ErrorResponse 錯誤回應
func ErrorResponse(message string, err error) APIResponse {
	response := APIResponse{
		Success: false,
		Message: message,
	}
	
	if err != nil {
		response.Error = err.Error()
	}
	
	return response
}
